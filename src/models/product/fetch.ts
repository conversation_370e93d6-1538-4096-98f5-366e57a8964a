import { productApiOption } from '@/services/api/product';
import request, { Data } from '@/utils/request';
import {
  BenefitCompare,
  BenefitListParam,
  BundleSku,
  BundleSkuListParam,
  CloudStorageBenefit,
  ProductSku,
  ProductSkuDetailParam,
  SuitableDeviceTypeEnum,
  SuitableProductListParam,
} from './interface';

// 获取适配的Product列表
export const fetchSuitableProductList = (
  param: SuitableProductListParam,
): Promise<Data<ProductSku>> => {
  const config = productApiOption.suitableProductList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 获取商品SKU的权益数据
export const fetchBenefitListByDevice = (
  param: BenefitListParam,
): Promise<Data<ProductSku>> => {
  const config = productApiOption.benefitListByDevice;
  config.option.params = { payload: JSON.stringify(param) };
  return request(config.url, config.option);
};

// 获取商品详情数据
export const fetchProductDetail = (
  param: ProductSkuDetailParam,
): Promise<ProductSku> => {
  const config = productApiOption.productDetail;
  config.option.params = param;
  return request(config.url, config.option);
};

// 创建微信支付订单
// export const fetchWeixinOrderCreation = (param: OrderCreationParam) => {
//   const config = productApiOption.weixinOrderCreation;
//   config.option.data = param;
//   return request(config.url, config.option);
// };

// 创建支付宝支付订单
// export const fetchAlipayOrderCreation = (param: OrderCreationParam) => {
//   const config = productApiOption.alipayOrderCreation;
//   config.option.data = param;
//   return request(config.url, config.option);
// };

export const fetchBenefitCompareDetail = (
  deviceType: SuitableDeviceTypeEnum,
): Promise<BenefitCompare> => {
  const config = productApiOption.benefitCompareDetail;
  config.option.params = { deviceType };
  return request(config.url, config.option);
};

// 获取默认权益列表（H5权益对比）
export const fetchDefaultBenefitList = (deviceType: SuitableDeviceTypeEnum): Promise<
  Data<CloudStorageBenefit>
> => {
  const config = productApiOption.defaultBenefitList;
  config.option.params = { deviceType };
  return request(config.url, config.option);
};

// 获取组合SKU列表
export const fetchBundleSkuList = (
  param?: BundleSkuListParam,
): Promise<BundleSku[]> => {
  const config = productApiOption.bundleSkuList;
  config.option.params = param || {};
  return request(config.url, config.option);
};

// 获取最低价格的组合SKU
export const fetchMinPriceBundleSku = (): Promise<BundleSku> => {
  const config = productApiOption.minPriceBundleSku;
  return request(config.url, config.option);
};
