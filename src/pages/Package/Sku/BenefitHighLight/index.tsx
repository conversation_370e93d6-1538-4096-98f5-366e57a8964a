import { ConnectState } from '@/models/connect';
import { CloudStorageBenefit } from '@/models/product/interface';
import { useSelector } from '@umijs/max';
import { Image, Swiper, SwiperRef } from 'antd-mobile';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const BenefitHighLight: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const progressRef = useRef<number>(0);
  const animationRef = useRef<number>();
  const swiperRef = useRef<SwiperRef>(null);

  const benefitList: CloudStorageBenefit[] = useSelector(
    ({ product }: ConnectState) =>
      product.defaultBenefitList.filter((benefit) => !!benefit.isCoreBenefit),
  );

  const startProgressAnimation = useCallback(() => {
    const startTime = Date.now();
    const duration = 3000; // 3秒

    const animate = () => {
      if (!isAutoPlaying) return;

      const elapsed = Date.now() - startTime;
      const newProgress = Math.min((elapsed / duration) * 100, 100);

      progressRef.current = newProgress;
      setProgress(newProgress);

      if (newProgress < 100) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        // 进度完成，切换到下一张
        const nextIndex = (currentIndex + 1) % benefitList.length;
        setCurrentIndex(nextIndex);
        if (swiperRef.current) {
          swiperRef.current.swipeTo(nextIndex);
        }
      }
    };

    animationRef.current = requestAnimationFrame(animate);
  }, [currentIndex, benefitList.length, isAutoPlaying]);

  const resetProgress = useCallback(() => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    progressRef.current = 0;
    setProgress(0);
  }, []);

  const handleSlideChange = useCallback(
    (index: number) => {
      setCurrentIndex(index);
      resetProgress();
    },
    [resetProgress],
  );

  const handleTouchStart = useCallback(() => {
    setIsAutoPlaying(false);
    // 不重置进度，保持倒计时效果
  }, []);

  const handleTouchEnd = useCallback(() => {
    setTimeout(() => {
      setIsAutoPlaying(true);
    }, 100);
  }, []);

  useEffect(() => {
    if (isAutoPlaying && benefitList.length > 1) {
      const timer = setTimeout(() => {
        startProgressAnimation();
      }, 100);
      return () => clearTimeout(timer);
    } else {
      resetProgress();
    }
  }, [currentIndex, isAutoPlaying, startProgressAnimation, resetProgress, benefitList.length]);

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  if (!benefitList || !benefitList.length) return <></>;

  return (
    <div className="mb-9">
      {/* 标题区域 */}
      <h2 className="text-3xl font-bold mb-5 active-color text-center">
        智见·守护·在线
        <br />
        全天候AI守护者
      </h2>

      {/* Swiper 轮播区域 */}
      <div
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleTouchStart}
        onMouseUp={handleTouchEnd}
      >
        <Swiper
          ref={swiperRef}
          indicator={(total, current) => (
            <div className="flex justify-center items-center mt-6 space-x-2">
              {Array.from({ length: total }).map((_, index) => (
                <div
                  key={index}
                  className={`transition-all duration-300 ${index === current
                    ? 'relative w-6 h-2 bg-gray-200 rounded-full overflow-hidden'
                    : 'w-2 h-2 rounded-full bg-gray-300'
                    }`}
                >
                  {index === current && (
                    <div
                      className="absolute top-0 left-0 h-full transition-all duration-100 ease-linear rounded-full bg-[#FF6907]"
                      style={{
                        width: `${progress}%`,
                      }}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
          className="w-full"
          allowTouchMove={true}
          stuckAtBoundary={false}
          onIndexChange={handleSlideChange}
          defaultIndex={0}
          loop={true}
        >
          {benefitList.map((benefit, index) => (
            <Swiper.Item key={String(index)}>
              <div className="text-center w-full flex flex-col justify-center items-center">
                <div className="flex justify-center mb-5 w-full">
                  <Image
                    src={benefit.image}
                    alt={benefit.name}
                    className="w-full"
                    style={{
                      objectFit: 'contain',
                    }}
                    placeholder="加载中..."
                  />
                </div>

                <div className="px-4">
                  <div className="mb-2 text-2xl font-bold text-secondary-color">
                    {benefit.name}
                  </div>
                  <p className="secondary-text-color text-sm leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </div>
            </Swiper.Item>
          ))}
        </Swiper>
      </div>


    </div>
  );
};

export default BenefitHighLight;
