import React, { useEffect, useMemo, useRef, useState } from 'react';
import './index.less';

export interface AdaptiveGridProps {
    children: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
    columnCount: number; // 总列数（包括第一列）
    firstColumnWidth?: number; // 第一列宽度
    minColumnWidth?: number; // 其他列最小宽度
    maxColumnWidth?: number; // 其他列最大宽度
    minVisibleColumns?: number; // 最少显示的列数（不包括第一列）
    showScrollHint?: boolean; // 是否显示滚动提示
    scrollHintText?: string; // 滚动提示文字
    onGridConfigChange?: (config: GridConfig) => void; // 网格配置变化回调
}

export interface GridConfig {
    gridTemplateColumns: string;
    minTableWidth: number;
    needsHorizontalScroll: boolean;
    actualColumnWidths: number[];
}

const AdaptiveGrid: React.FC<AdaptiveGridProps> = ({
    children,
    className = '',
    style,
    columnCount,
    firstColumnWidth,
    minColumnWidth,
    maxColumnWidth,
    minVisibleColumns = 2,
    showScrollHint = true,
    scrollHintText = '← 左右滑动查看更多内容 →',
    onGridConfigChange,
}) => {
    const [containerWidth, setContainerWidth] = useState<number>(0);
    const containerRef = useRef<HTMLDivElement>(null);

    // 监听容器宽度变化
    useEffect(() => {
        const updateWidth = () => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.offsetWidth);
            }
        };

        updateWidth();
        window.addEventListener('resize', updateWidth);
        return () => window.removeEventListener('resize', updateWidth);
    }, []);

    // 计算自适应网格配置
    const gridConfig = useMemo(() => {
        if (!containerWidth || columnCount <= 0) {
            return {
                gridTemplateColumns: '',
                minTableWidth: 0,
                needsHorizontalScroll: false,
                actualColumnWidths: [],
            };
        }

        // 根据屏幕宽度调整参数
        const isMobile = containerWidth < 768;

        // 第一列宽度配置
        const actualFirstColumnWidth = firstColumnWidth || (isMobile ? 100 : 120);

        // 其他列宽度配置
        const actualMinColumnWidth = minColumnWidth || (isMobile ? 70 : 80);
        const actualMaxColumnWidth = maxColumnWidth || (isMobile ? 100 : 120);

        const otherColumnsCount = columnCount - 1;
        const availableWidth = containerWidth - actualFirstColumnWidth;

        if (otherColumnsCount <= 0) {
            return {
                gridTemplateColumns: `${actualFirstColumnWidth}px`,
                minTableWidth: actualFirstColumnWidth,
                needsHorizontalScroll: false,
                actualColumnWidths: [actualFirstColumnWidth],
            };
        }

        // 确保最少显示指定数量的列
        const minVisibleOtherColumns = Math.min(minVisibleColumns, otherColumnsCount);
        const maxVisibleOtherColumns = Math.max(
            minVisibleOtherColumns,
            Math.floor(availableWidth / actualMinColumnWidth)
        );

        // 判断是否需要水平滚动
        const needsHorizontalScroll = otherColumnsCount > maxVisibleOtherColumns;

        // 计算实际列宽
        const actualColumnWidths: number[] = [actualFirstColumnWidth];

        let otherColumnWidth: number;
        if (needsHorizontalScroll) {
            // 需要滚动时，使用最小宽度
            otherColumnWidth = actualMinColumnWidth;
        } else {
            // 不需要滚动时，自适应宽度但不超过最大宽度
            const calculatedWidth = availableWidth / otherColumnsCount;
            otherColumnWidth = Math.min(actualMaxColumnWidth, Math.max(actualMinColumnWidth, calculatedWidth));
        }

        // 填充其他列宽度
        for (let i = 0; i < otherColumnsCount; i++) {
            actualColumnWidths.push(otherColumnWidth);
        }

        const gridTemplateColumns = actualColumnWidths.map(width => `${width}px`).join(' ');
        const minTableWidth = actualColumnWidths.reduce((sum, width) => sum + width, 0);

        const config = {
            gridTemplateColumns,
            minTableWidth,
            needsHorizontalScroll,
            actualColumnWidths,
        };

        // 通知外部组件网格配置变化
        onGridConfigChange?.(config);

        return config;
    }, [
        containerWidth,
        columnCount,
        firstColumnWidth,
        minColumnWidth,
        maxColumnWidth,
        minVisibleColumns,
        onGridConfigChange,
    ]);

    const gridStyle = {
        gridTemplateColumns: gridConfig.gridTemplateColumns,
        minWidth: `${gridConfig.minTableWidth}px`,
        width: gridConfig.needsHorizontalScroll ? `${gridConfig.minTableWidth}px` : '100%',
        ...style,
    };

    return (
        <div className={`adaptive-grid-container ${className}`}>
            <div className="adaptive-grid-wrapper" ref={containerRef}>
                {/* 滚动提示 */}
                {showScrollHint && gridConfig.needsHorizontalScroll && (
                    <div className="adaptive-grid-scroll-hint">
                        {scrollHintText}
                    </div>
                )}

                <div className={`adaptive-grid-scroll ${gridConfig.needsHorizontalScroll ? 'scroll-enabled' : ''}`}>
                    <div className="adaptive-grid" style={gridStyle}>
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdaptiveGrid;