.adaptive-grid-container {
    width: 100%;

    .adaptive-grid-wrapper {
        overflow: hidden;
    }

    .adaptive-grid-scroll-hint {
        text-align: center;
        font-size: 12px;
        margin-bottom: 8px;
        opacity: 0.7;
        color: var(--secondary-text-color, #666);
    }

    .adaptive-grid-scroll {
        overflow-x: auto;

        &.scroll-enabled {
            // 启用平滑滚动
            scroll-behavior: smooth;

            // 优化触摸滚动
            -webkit-overflow-scrolling: touch;

            // 防止滚动时的弹性效果
            overscroll-behavior-x: contain;

            // 桌面端滚动条样式
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

            &::-webkit-scrollbar {
                height: 4px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(0, 0, 0, 0.05);
                border-radius: 2px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.2);
                border-radius: 2px;

                &:hover {
                    background: rgba(0, 0, 0, 0.3);
                }

                &:active {
                    background: rgba(0, 0, 0, 0.4);
                }
            }

            // 移动端隐藏滚动条但保持滚动功能
            @media (max-width: 768px) {
                scrollbar-width: none;

                &::-webkit-scrollbar {
                    display: none;
                }
            }
        }
    }

    .adaptive-grid {
        display: grid;
        gap: 0;
    }
}