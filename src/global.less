@import url('./assets/styles/variables');

@font-face {
  font-family: u2000;
  src: url("https://instructions.petkit.com/fonts/u2000.woff2") format("woff2");
  font-weight: 400;
  font-style: normal
}

:root:root {
  --adm-font-family: -apple-system, blinkmacsystemfont, 'u2000', 'Helvetica Neue', helvetica, segoe ui, arial, roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

html,
body,
#root,
.keep-alive,
#\/confirm,
#\/order,
#\/payment\/result,
#\/subscription\/result {
  width: 100%;
  height: 100%;
}

// * {
//   font-family: u2000;
// }

//

dd {
  margin-bottom: 0 !important;
}

.keep-alive-container {
  width: 100%;
  height: 100%;
}

body {
  background-color: #f6f7f9;
  padding: 0;
}

:root:root {
  --fixed-active-line-width: 18px;
  --adm-radio-icon-size: 16px;
}

.bg-color {
  background-color: linear-gradient(180deg, #fffcf5 0%, rgba(255, 255, 255, 0.8) 100%);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.colorful-background {
  background: url(./assets/background.png) 100% no-repeat;
  background-size: 100% 100%;
  background-color: #fff;
}

.default-color {
  color: @default-color;
}

.active-color {
  color: @active-color;
}

.price-color {
  color: @price-color;
}

.primary-color {
  color: @primary-color;
}

.primary-text-color {
  color: @primary-text-color;
}

.secondary-text-color {
  color: @secondary-text-color;
}

.gray-text-color {
  color: @gray-text-color;
}

.warning-color {
  color: @warning-color;
}

.sku-item-selected-bg-color {
  background: @sku-item-selected-bg-color;
}

.sku-item-selected-border-color {
  // border-color: @sku-item-selected-border-color;
  border-color: @primary-color;
}

.sku-item-bg-color {
  background: @sku-item-bg-color;
}

.normal-color {
  color: @normal-color;
}

.table-bg-color {
  background-color: @table-bg-color;
}

.combination-plan-bg-color {
  background: @combination-plan-bg-color;
}

.combination-plan-color {
  color: @combination-plan-color;
}

// 隐藏滚动条
.no-scroll-bar {
  width: 100%;
  box-sizing: border-box;
  overflow-x: scroll;
  /* 设置溢出滚动 */
  white-space: nowrap;
  overflow-y: hidden;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

.no-scroll-bar::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

// 自定义样式
.payment-container {
  .adm {
    &-card {
      &-header {
        &-title {
          font-size: 1rem;
        }
      }
    }

    &-list {
      &-body {
        border: none;
      }

      &-item {
        &:active {
          background-color: transparent !important;
        }

        &-content {
          padding-right: 0;
        }
      }
    }
  }
}

.auto-pay-checkbox {
  .adm {
    &-checkbox {
      display: flex;
      align-items: center;

      &-icon {
        border-color: #3378f8;
        width: 16px;
        height: 16px;
      }

      &-content {
        color: #3378f8;
        font-size: 0.625rem;
      }
    }
  }
}

.adm-error-block-image>img {
  width: 220px;
  height: 146px;
  margin: 0 auto;
}

.adm-capsule-tabs-header {
  display: flex;
  justify-content: center;
}

.adm-capsule-tabs-tab {
  font-weight: 600;
}

.padding-top-safe-area-iso {
  /* 适配齐刘海 */
  padding-top: constant(safe-area-inset-top) !important;
  /* 适配齐刘海 */
  padding-top: env(safe-area-inset-top) !important;
}

.padding-bottom-safe-area-iso {
  /* 适配底部黑条 */
  // padding-bottom: constant(safe-area-inset-bottom);
  // /* 适配底部黑条 */
  // padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: 20px !important;
}

.padding-safe-area-ios {
  &:extend(.padding-top-safe-area-iso);
  &:extend(.padding-bottom-safe-area-iso);
}

.padding-top-safe-area-android {
  padding-top: 45px !important;
}

.padding-top-safe-area-harmony {
  padding-top: 45px !important;
}

.padding-bottom-safe-area-android {
  // padding-bottom: constant(safe-area-inset-bottom);
}

// 鸿蒙底部安全距离
.padding-bottom-safe-area-harmony {
  // padding-bottom: 100px !important;
}

.padding-safe-area-android {
  &:extend(.padding-top-safe-area-android);
  &:extend(.padding-bottom-safe-area-android);
}

.padding-safe-area-harmony {
  &:extend(.padding-top-safe-area-harmony);
  &:extend(.padding-bottom-safe-area-harmony);
}

.adm-toast-mask .adm-toast-main {
  --toast-bg-color: rgba(0, 0, 0, 0.7);
  background-color: var(--toast-bg-color) !important;
}

.auto-pay-checkbox .adm-checkbox-content {
  font-size: 0.875rem !important;
}

video::-internal-media-controls-download-button {
  display: none;
}

video::-webkit-media-controls-enclosure {
  overflow: hidden;
}

video::-webkit-media-controls-panel {
  width: calc(100% + 50px);
}


iframe[title='Checkout'] {
  border: 1px solid red !important;
}

.no-scrollbar {
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 自适应表格滚动条样式
.adaptive-table-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

  // 启用平滑滚动
  scroll-behavior: smooth;

  // 优化触摸滚动
  -webkit-overflow-scrolling: touch;

  // 防止滚动时的弹性效果
  overscroll-behavior-x: contain;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }

    &:active {
      background: rgba(0, 0, 0, 0.4);
    }
  }

  // 移动端隐藏滚动条但保持滚动功能
  @media (max-width: 768px) {
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}